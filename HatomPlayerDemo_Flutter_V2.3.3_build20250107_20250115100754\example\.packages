# This file is deprecated. Tools should instead consume 
# `.dart_tool/package_config.json`.
# 
# For more info see: https://dart.dev/go/dot-packages-deprecation
# 
# Generated by pub on 2024-05-21 10:20:51.391041.
async:file:///E:/pub_cache/hosted/pub.flutter-io.cn/async-2.8.2/lib/
boolean_selector:file:///E:/pub_cache/hosted/pub.flutter-io.cn/boolean_selector-2.1.0/lib/
characters:file:///E:/pub_cache/hosted/pub.flutter-io.cn/characters-1.2.0/lib/
charcode:file:///E:/pub_cache/hosted/pub.flutter-io.cn/charcode-1.3.1/lib/
clock:file:///E:/pub_cache/hosted/pub.flutter-io.cn/clock-1.1.0/lib/
collection:file:///E:/pub_cache/hosted/pub.flutter-io.cn/collection-1.16.0/lib/
cupertino_icons:file:///E:/pub_cache/hosted/pub.flutter-io.cn/cupertino_icons-1.0.5/lib/
fake_async:file:///E:/pub_cache/hosted/pub.flutter-io.cn/fake_async-1.3.0/lib/
ffi:file:///E:/pub_cache/hosted/pub.flutter-io.cn/ffi-2.0.1/lib/
file:file:///E:/pub_cache/hosted/pub.flutter-io.cn/file-6.1.4/lib/
flutter:file:///D:/flutterSDK/flutter_windows_3.0.5-stable/packages/flutter/lib/
flutter_easyloading:file:///E:/pub_cache/hosted/pub.flutter-io.cn/flutter_easyloading-3.0.5/lib/
flutter_hatomplayer:../lib/
flutter_lints:file:///E:/pub_cache/hosted/pub.flutter-io.cn/flutter_lints-1.0.4/lib/
flutter_spinkit:file:///E:/pub_cache/hosted/pub.flutter-io.cn/flutter_spinkit-5.1.0/lib/
flutter_test:file:///D:/flutterSDK/flutter_windows_3.0.5-stable/packages/flutter_test/lib/
image_gallery_saver:file:///E:/pub_cache/hosted/pub.flutter-io.cn/image_gallery_saver-1.7.1/lib/
lints:file:///E:/pub_cache/hosted/pub.flutter-io.cn/lints-1.0.1/lib/
matcher:file:///E:/pub_cache/hosted/pub.flutter-io.cn/matcher-0.12.11/lib/
material_color_utilities:file:///E:/pub_cache/hosted/pub.flutter-io.cn/material_color_utilities-0.1.4/lib/
meta:file:///E:/pub_cache/hosted/pub.flutter-io.cn/meta-1.7.0/lib/
path:file:///E:/pub_cache/hosted/pub.flutter-io.cn/path-1.8.1/lib/
path_provider:file:///E:/pub_cache/hosted/pub.flutter-io.cn/path_provider-2.0.11/lib/
path_provider_android:file:///E:/pub_cache/hosted/pub.flutter-io.cn/path_provider_android-2.0.20/lib/
path_provider_ios:file:///E:/pub_cache/hosted/pub.flutter-io.cn/path_provider_ios-2.0.11/lib/
path_provider_linux:file:///E:/pub_cache/hosted/pub.flutter-io.cn/path_provider_linux-2.1.7/lib/
path_provider_macos:file:///E:/pub_cache/hosted/pub.flutter-io.cn/path_provider_macos-2.0.6/lib/
path_provider_platform_interface:file:///E:/pub_cache/hosted/pub.flutter-io.cn/path_provider_platform_interface-2.0.4/lib/
path_provider_windows:file:///E:/pub_cache/hosted/pub.flutter-io.cn/path_provider_windows-2.1.3/lib/
permission_handler:file:///E:/pub_cache/hosted/pub.flutter-io.cn/permission_handler-9.2.0/lib/
permission_handler_android:file:///E:/pub_cache/hosted/pub.flutter-io.cn/permission_handler_android-9.0.2+1/lib/
permission_handler_apple:file:///E:/pub_cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.0.4/lib/
permission_handler_platform_interface:file:///E:/pub_cache/hosted/pub.flutter-io.cn/permission_handler_platform_interface-3.7.1/lib/
permission_handler_windows:file:///E:/pub_cache/hosted/pub.flutter-io.cn/permission_handler_windows-0.1.0/lib/
platform:file:///E:/pub_cache/hosted/pub.flutter-io.cn/platform-3.1.0/lib/
plugin_platform_interface:file:///E:/pub_cache/hosted/pub.flutter-io.cn/plugin_platform_interface-2.1.3/lib/
process:file:///E:/pub_cache/hosted/pub.flutter-io.cn/process-4.2.4/lib/
sky_engine:file:///D:/flutterSDK/flutter_windows_3.0.5-stable/bin/cache/pkg/sky_engine/lib/
source_span:file:///E:/pub_cache/hosted/pub.flutter-io.cn/source_span-1.8.2/lib/
stack_trace:file:///E:/pub_cache/hosted/pub.flutter-io.cn/stack_trace-1.10.0/lib/
stream_channel:file:///E:/pub_cache/hosted/pub.flutter-io.cn/stream_channel-2.1.0/lib/
string_scanner:file:///E:/pub_cache/hosted/pub.flutter-io.cn/string_scanner-1.1.0/lib/
term_glyph:file:///E:/pub_cache/hosted/pub.flutter-io.cn/term_glyph-1.2.0/lib/
test_api:file:///E:/pub_cache/hosted/pub.flutter-io.cn/test_api-0.4.9/lib/
vector_math:file:///E:/pub_cache/hosted/pub.flutter-io.cn/vector_math-2.1.2/lib/
win32:file:///E:/pub_cache/hosted/pub.flutter-io.cn/win32-3.0.0/lib/
xdg_directories:file:///E:/pub_cache/hosted/pub.flutter-io.cn/xdg_directories-0.2.0+2/lib/
flutter_hatomplayer_example:lib/
