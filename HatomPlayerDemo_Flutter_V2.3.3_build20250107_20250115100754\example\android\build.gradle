buildscript {
    ext.kotlin_version = '1.5.30'
    repositories {
        maven {
            url "http://af.hikvision.com.cn:80/artifactory/maven-down/"
            credentials {
                username "maven-down"
                password "maven-down"
            }
        }
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:4.1.0'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
    }
}

allprojects {
    repositories {
        maven {
            url "http://af.hikvision.com.cn:80/artifactory/maven-down/"
            credentials {
                username "maven-down"
                password "maven-down"
            }
        }
        google()
        mavenCentral()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.layout.buildDirectory
}
