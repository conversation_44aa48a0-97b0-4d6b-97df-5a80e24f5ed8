package com.hikvision.isms.flutter_hatomplayer.utils;

public class FlutterPlayerConstants {

    public static int RESULT_SUCCESS = 0;
    public static int RESULT_FAIL = -1;

    public static String SET_PLAY_CONFIG = "setPlayConfig";
    public static String SET_DATA_SOURCE = "setDataSource";
    public static String START = "start";
    public static String STOP = "stop";
    public static String ENABLE_AUDIO = "enableAudio";
    public static String SCREEN_SHOOT = "screenshoot";
    public static String CHANGE_STREAM = "changeStream";
    public static String INIT_PLAYER = "initPlayer";
    public static String SEEK_PLAYBACK = "seekPlayback";
    public static String PAUSE = "pause";
    public static String RESUME = "resume";
    public static String GET_PLAYBACK_SPEED = "getPlaybackSpeed";
    public static String SET_PLAYBACK_SPEED = "setPlaybackSpeed";
    public static String START_RECORD = "startRecord";
    public static String STOP_RECORD = "stopRecord";
    public static String START_RECORD_AND_CONVERT = "startRecordAndConvert";
    public static String GET_TOTAL_TRAFFIC = "getTotalTraffic";
    public static String PLAY_FILE = "playFile";
    public static String GET_TOTAL_TIME = "getTotalTime";
    public static String GET_PLAYED_TIME = "getPlayedTime";
    public static String SET_CURRENT_FRAME = "setCurrentFrame";
    public static String GET_OSDTIME = "getOSDTime";
    public static String SET_VOICE_DATA_SOURCE = "setVoiceDataSource";
    public static String START_VOICE_TALK = "startVoiceTalk";
    public static String STOP_VOICE_TALK = "stopVoiceTalk";
    public static String SET_DECODE_THREAD_NUM = "setDecodeThreadNum";
    public static String GET_FRAME_RATE = "getFrameRate";
    public static String SET_EXPECTED_FRAME_RATE = "setExpectedFrameRate";
    public static String RELEASE = "release";

}
