import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 海康威视视频播放页面 - 演示版本 (TV优化版)
class DemoVideoPlayerPage extends StatefulWidget {
  const DemoVideoPlayerPage({super.key});

  @override
  State<StatefulWidget> createState() => _DemoVideoPlayerPageState();
}

class _DemoVideoPlayerPageState extends State<DemoVideoPlayerPage> {
  /// 播放地址 - 使用用户提供的RTSP地址
  static const String playUrl = 'https://jsoncompare.org/LearningContainer/SampleFiles/Video/MP4/Sample-Video-File-For-Testing.mp4';

  /// 播放状态
  bool isPlaying = false;
  bool isConnecting = false;

  /// 错误信息
  String? errorMessage;

  /// 播放地址输入框控制器
  final TextEditingController playUrlController = TextEditingController(text: playUrl);

  @override
  void initState() {
    super.initState();
    // 请求权限
    _requestPermissions();
  }

  /// 请求必要权限
  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      await Permission.storage.request();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      body: SafeArea(
        child: Row(
          children: [
            // 左侧视频播放区域 (占据大部分空间)
            Expanded(
              flex: 3,
              child: Container(
                margin: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  color: Colors.black,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.grey.shade800, width: 2),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(10),
                  child: _buildVideoWidget(),
                ),
              ),
            ),
            // 右侧控制区域
            Expanded(
              flex: 1,
              child: Container(
                padding: const EdgeInsets.all(16.0),
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.stretch,
                    children: [
                      // 标题
                      Text(
                        '海康威视播放器',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 20),

                      // RTSP地址输入
                      Text(
                        'RTSP播放地址:',
                        style: TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: playUrlController,
                        style: const TextStyle(color: Colors.white, fontSize: 14),
                        decoration: InputDecoration(
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade600),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade600),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: const BorderSide(color: Colors.blue, width: 2),
                          ),
                          hintText: '请输入RTSP播放地址',
                          hintStyle: TextStyle(color: Colors.grey.shade400),
                          filled: true,
                          fillColor: Colors.grey.shade900,
                          contentPadding: const EdgeInsets.all(12),
                        ),
                        maxLines: 2,
                      ),
                      const SizedBox(height: 20),

                      // 播放控制按钮 (TV优化 - 适中的按钮)
                      SizedBox(
                        height: 50,
                        child: ElevatedButton.icon(
                          onPressed: (isPlaying || isConnecting) ? null : _startPlay,
                          icon: const Icon(Icons.play_arrow, size: 24),
                          label: const Text('开始播放', style: TextStyle(fontSize: 16)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.green.shade600,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(height: 12),
                      SizedBox(
                        height: 50,
                        child: ElevatedButton.icon(
                          onPressed: (!isPlaying && !isConnecting) ? null : _stopPlay,
                          icon: const Icon(Icons.stop, size: 24),
                          label: const Text('停止播放', style: TextStyle(fontSize: 16)),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red.shade600,
                            foregroundColor: Colors.white,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                          ),
                        ),
                      ),
                      const SizedBox(height: 20),

                      // 状态信息区域
                      // 错误信息
                      if (errorMessage != null)
                        Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.red.shade900.withValues(alpha: 0.3),
                            border: Border.all(color: Colors.red.shade600),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.error, color: Colors.red.shade400, size: 20),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  '播放失败: $errorMessage',
                                  style: TextStyle(
                                    color: Colors.red.shade300,
                                    fontSize: 14,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),

                      // 播放状态信息
                      if (isPlaying && errorMessage == null)
                        Container(
                          margin: const EdgeInsets.only(bottom: 12),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.green.shade900.withValues(alpha: 0.3),
                            border: Border.all(color: Colors.green.shade600),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Row(
                            children: [
                              Icon(Icons.play_circle, color: Colors.green.shade400, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                '正在播放 (演示模式)',
                                style: TextStyle(
                                  color: Colors.green.shade300,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                        ),

                      // 演示说明
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade900.withValues(alpha: 0.3),
                          border: Border.all(color: Colors.blue.shade600),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(Icons.info, color: Colors.blue.shade400, size: 20),
                                const SizedBox(width: 8),
                                Text(
                                  '演示说明',
                                  style: TextStyle(
                                    color: Colors.blue.shade300,
                                    fontWeight: FontWeight.bold,
                                    fontSize: 16,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '这是一个演示版本，海康威视SDK已集成但需要真机调试。\n\n'
                              '项目结构已完成，包含：\n'
                              '• Flutter Dart代码\n'
                              '• Android原生代码\n'
                              '• iOS原生代码\n'
                              '• 所需的库文件和配置\n\n'
                              '遥控器操作：\n'
                              '• 方向键导航\n'
                              '• 确认键选择\n'
                              '• 返回键退出',
                              style: TextStyle(
                                color: Colors.blue.shade300,
                                fontSize: 12,
                                height: 1.3,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建视频播放widget (TV优化版)
  Widget _buildVideoWidget() {
    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.red.shade300),
            const SizedBox(height: 16),
            Text(
              '播放失败',
              style: TextStyle(color: Colors.red.shade300, fontSize: 24),
            ),
            const SizedBox(height: 8),
            Text(
              errorMessage!,
              style: TextStyle(color: Colors.grey.shade400, fontSize: 16),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    if (isConnecting) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const SizedBox(
              width: 60,
              height: 60,
              child: CircularProgressIndicator(
                color: Colors.white,
                strokeWidth: 4,
              ),
            ),
            const SizedBox(height: 24),
            const Text(
              '正在连接...',
              style: TextStyle(color: Colors.white, fontSize: 20),
            ),
          ],
        ),
      );
    }

    if (isPlaying) {
      return Stack(
        children: [
          // 模拟视频背景
          Container(
            width: double.infinity,
            height: double.infinity,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  Colors.blue.shade900,
                  Colors.purple.shade900,
                  Colors.green.shade900,
                ],
              ),
            ),
          ),
          // 播放状态指示
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.play_circle_filled, size: 100, color: Colors.green.shade300),
                const SizedBox(height: 24),
                Text(
                  '演示播放中',
                  style: TextStyle(color: Colors.green.shade300, fontSize: 28),
                ),
                const SizedBox(height: 12),
                Text(
                  '真实视频需要在真机上运行',
                  style: TextStyle(color: Colors.grey.shade300, fontSize: 18),
                ),
              ],
            ),
          ),
        ],
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.videocam_off, size: 80, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            '点击右侧开始播放按钮',
            style: TextStyle(color: Colors.grey.shade400, fontSize: 20),
          ),
          const SizedBox(height: 8),
          Text(
            '使用遥控器方向键导航',
            style: TextStyle(color: Colors.grey.shade500, fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// 开始播放
  Future<void> _startPlay() async {
    setState(() {
      isConnecting = true;
      errorMessage = null;
    });

    // 模拟连接过程
    await Future.delayed(const Duration(seconds: 2));

    // 模拟播放成功
    setState(() {
      isConnecting = false;
      isPlaying = true;
    });

    _showMessage('播放开始 (演示模式)', isError: false);
  }

  /// 停止播放
  Future<void> _stopPlay() async {
    setState(() {
      isPlaying = false;
      isConnecting = false;
      errorMessage = null;
    });

    _showMessage('播放停止', isError: false);
  }

  /// 显示消息
  void _showMessage(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
