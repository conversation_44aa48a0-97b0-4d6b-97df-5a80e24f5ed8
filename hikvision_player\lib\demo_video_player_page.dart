import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 海康威视视频播放页面 - 演示版本
class DemoVideoPlayerPage extends StatefulWidget {
  const DemoVideoPlayerPage({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _DemoVideoPlayerPageState();
}

class _DemoVideoPlayerPageState extends State<DemoVideoPlayerPage> {
  /// 播放地址 - 使用用户提供的RTSP地址
  static const String playUrl = 'https://jsoncompare.org/LearningContainer/SampleFiles/Video/MP4/Sample-Video-File-For-Testing.mp4';

  /// 播放状态
  bool isPlaying = false;
  bool isConnecting = false;

  /// 错误信息
  String? errorMessage;

  /// 播放地址输入框控制器
  final TextEditingController playUrlController = TextEditingController(text: playUrl);

  @override
  void initState() {
    super.initState();
    // 请求权限
    _requestPermissions();
  }

  /// 请求必要权限
  Future<void> _requestPermissions() async {
    if (Platform.isAndroid) {
      await Permission.storage.request();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('海康威视播放器 - 演示版'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: Column(
        children: [
          // 视频播放区域
          Container(
            width: MediaQuery.of(context).size.width,
            height: MediaQuery.of(context).size.width * 9 / 16,
            color: Colors.black,
            child: _buildVideoWidget(),
          ),
          // 控制区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'RTSP播放地址:',
                    style: TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
                  ),
                  const SizedBox(height: 8),
                  TextField(
                    controller: playUrlController,
                    decoration: const InputDecoration(
                      border: OutlineInputBorder(),
                      hintText: '请输入RTSP播放地址',
                    ),
                    maxLines: 2,
                  ),
                  const SizedBox(height: 16),
                  // 播放控制按钮
                  Row(
                    children: [
                      Expanded(
                        child: ElevatedButton(
                          onPressed: (isPlaying || isConnecting) ? null : _startPlay,
                          child: const Text('开始播放'),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton(
                          onPressed: (!isPlaying && !isConnecting) ? null : _stopPlay,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.red,
                            foregroundColor: Colors.white,
                          ),
                          child: const Text('停止播放'),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  // 状态信息
                  if (errorMessage != null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        border: Border.all(color: Colors.red.shade200),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.error, color: Colors.red.shade600),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              '播放失败: $errorMessage',
                              style: TextStyle(color: Colors.red.shade700),
                            ),
                          ),
                        ],
                      ),
                    ),
                  if (isPlaying && errorMessage == null)
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.green.shade50,
                        border: Border.all(color: Colors.green.shade200),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.play_circle, color: Colors.green.shade600),
                          const SizedBox(width: 8),
                          Text(
                            '正在播放 (演示模式)',
                            style: TextStyle(color: Colors.green.shade700),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 16),
                  // 演示说明
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      border: Border.all(color: Colors.blue.shade200),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info, color: Colors.blue.shade600),
                            const SizedBox(width: 8),
                            Text(
                              '演示说明',
                              style: TextStyle(
                                color: Colors.blue.shade700,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '这是一个演示版本，海康威视SDK已集成但需要真机调试。\n'
                          '项目结构已完成，包含：\n'
                          '• Flutter Dart代码\n'
                          '• Android原生代码\n'
                          '• iOS原生代码\n'
                          '• 所需的库文件和配置',
                          style: TextStyle(color: Colors.blue.shade700),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建视频播放widget
  Widget _buildVideoWidget() {
    if (errorMessage != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 48, color: Colors.red.shade300),
            const SizedBox(height: 8),
            Text(
              '播放失败',
              style: TextStyle(color: Colors.red.shade300, fontSize: 16),
            ),
          ],
        ),
      );
    }

    if (isConnecting) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const CircularProgressIndicator(color: Colors.white),
            const SizedBox(height: 16),
            const Text(
              '正在连接...',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
      );
    }

    if (isPlaying) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.play_circle_filled, size: 64, color: Colors.green.shade300),
            const SizedBox(height: 16),
            Text(
              '演示播放中',
              style: TextStyle(color: Colors.green.shade300, fontSize: 18),
            ),
            const SizedBox(height: 8),
            Text(
              '真实视频需要在真机上运行',
              style: TextStyle(color: Colors.grey.shade400, fontSize: 14),
            ),
          ],
        ),
      );
    }

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.videocam_off, size: 48, color: Colors.grey.shade400),
          const SizedBox(height: 8),
          Text(
            '点击开始播放',
            style: TextStyle(color: Colors.grey.shade400, fontSize: 16),
          ),
        ],
      ),
    );
  }

  /// 开始播放
  Future<void> _startPlay() async {
    setState(() {
      isConnecting = true;
      errorMessage = null;
    });

    // 模拟连接过程
    await Future.delayed(const Duration(seconds: 2));

    // 模拟播放成功
    setState(() {
      isConnecting = false;
      isPlaying = true;
    });

    _showMessage('播放开始 (演示模式)', isError: false);
  }

  /// 停止播放
  Future<void> _stopPlay() async {
    setState(() {
      isPlaying = false;
      isConnecting = false;
      errorMessage = null;
    });

    _showMessage('播放停止', isError: false);
  }

  /// 显示消息
  void _showMessage(String message, {bool isError = false}) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : Colors.green,
          duration: const Duration(seconds: 2),
        ),
      );
    }
  }
}
