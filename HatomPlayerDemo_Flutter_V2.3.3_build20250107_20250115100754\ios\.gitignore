.idea/
.vagrant/
.sconsign.dblite
.svn/

.DS_Store
*.swp
profile

DerivedData/
build/
GeneratedPluginRegistrant.h
GeneratedPluginRegistrant.m

.generated/

*.pbxuser
*.mode1v3
*.mode2v3
*.perspectivev3

!default.pbxuser
!default.mode1v3
!default.mode2v3
!default.perspectivev3

xcuserdata

*.moved-aside

*.pyc
*sync/
Icon?
.tags*

/Flutter/Generated.xcconfig
/Flutter/ephemeral/
/Flutter/flutter_export_environment.sh