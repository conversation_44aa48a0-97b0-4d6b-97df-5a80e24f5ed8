def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {
    compileSdkVersion flutter.compileSdkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    defaultConfig {
        // TODO: Specify your own unique Application ID (https://developer.android.com/studio/build/application-id.html).
        applicationId "com.hikvision.isms.flutter_hatomplayer_example"
        minSdkVersion flutter.minSdkVersion
        targetSdkVersion flutter.targetSdkVersion
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        ndk{
            abiFilters 'arm64-v8a','armeabi-v7a'
        }
    }


    signingConfigs {
        config {
            keyAlias 'demo'
            keyPassword 'demo12345'
            storeFile file('/demo.jks')
            storePassword 'demo12345'
            v1SigningEnabled true
            v2SigningEnabled true
        }
    }

    buildTypes {
        release {
            //是否zip对齐
            zipAlignEnabled true
            // 缩减resource文件
            shrinkResources false
            //Proguard
            minifyEnabled true
            debuggable false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
            //签名
            signingConfig signingConfigs.config
        }
        debug {
            //给applicationId添加后缀“.debug”
            applicationIdSuffix ".debug"
            buildConfigField "boolean", "LOG_DEBUG", "true"
            zipAlignEnabled false
            shrinkResources false
            minifyEnabled false
            debuggable true
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
//    implementation "com.hikvision.stream:hcnet-streamclient:1.0.0-SNAPSHOT"
//    implementation "com.hikvision.stream:np-streamclient:1.0.0-SNAPSHOT"
//    implementation 'com.hikvision.sdk:hik-hcnetsdk:6.1.9.3_build20220126-SNAPSHOT'
}
