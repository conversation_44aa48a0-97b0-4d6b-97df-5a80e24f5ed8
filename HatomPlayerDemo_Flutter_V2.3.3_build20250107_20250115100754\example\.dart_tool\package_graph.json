{"roots": ["flutter_hatomplayer_example"], "packages": [{"name": "flutter_hatomplayer_example", "version": "0.0.0", "dependencies": ["cupertino_icons", "flutter", "flutter_easyloading", "flutter_hatomplayer", "image_gallery_saver", "path_provider", "permission_handler"], "devDependencies": ["flutter_lints", "flutter_test"]}, {"name": "flutter_test", "version": "0.0.0", "dependencies": ["async", "boolean_selector", "characters", "clock", "collection", "fake_async", "flutter", "leak_tracker", "leak_tracker_flutter_testing", "leak_tracker_testing", "matcher", "material_color_utilities", "meta", "path", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph", "test_api", "vector_math", "vm_service"]}, {"name": "permission_handler", "version": "9.2.0", "dependencies": ["flutter", "meta", "permission_handler_android", "permission_handler_apple", "permission_handler_platform_interface", "permission_handler_windows"]}, {"name": "flutter_easyloading", "version": "3.0.5", "dependencies": ["flutter", "flutter_spinkit"]}, {"name": "image_gallery_saver", "version": "1.7.1", "dependencies": ["flutter"]}, {"name": "flutter_hatomplayer", "version": "0.0.1", "dependencies": ["flutter"]}, {"name": "flutter", "version": "0.0.0", "dependencies": ["characters", "collection", "material_color_utilities", "meta", "sky_engine", "vector_math"]}, {"name": "vm_service", "version": "15.0.0", "dependencies": []}, {"name": "term_glyph", "version": "1.2.2", "dependencies": []}, {"name": "string_scanner", "version": "1.4.1", "dependencies": ["source_span"]}, {"name": "stream_channel", "version": "2.1.4", "dependencies": ["async"]}, {"name": "source_span", "version": "1.10.1", "dependencies": ["collection", "path", "term_glyph"]}, {"name": "meta", "version": "1.16.0", "dependencies": []}, {"name": "material_color_utilities", "version": "0.11.1", "dependencies": ["collection"]}, {"name": "leak_tracker_testing", "version": "3.0.1", "dependencies": ["leak_tracker", "matcher", "meta"]}, {"name": "leak_tracker", "version": "10.0.9", "dependencies": ["clock", "collection", "meta", "path", "vm_service"]}, {"name": "collection", "version": "1.19.1", "dependencies": []}, {"name": "characters", "version": "1.4.0", "dependencies": []}, {"name": "boolean_selector", "version": "2.1.2", "dependencies": ["source_span", "string_scanner"]}, {"name": "async", "version": "2.13.0", "dependencies": ["collection", "meta"]}, {"name": "leak_tracker_flutter_testing", "version": "3.0.9", "dependencies": ["flutter", "leak_tracker", "leak_tracker_testing", "matcher", "meta"]}, {"name": "vector_math", "version": "2.1.4", "dependencies": []}, {"name": "stack_trace", "version": "1.12.1", "dependencies": ["path"]}, {"name": "clock", "version": "1.1.2", "dependencies": []}, {"name": "fake_async", "version": "1.3.3", "dependencies": ["clock", "collection"]}, {"name": "path", "version": "1.9.1", "dependencies": []}, {"name": "matcher", "version": "0.12.17", "dependencies": ["async", "meta", "stack_trace", "term_glyph", "test_api"]}, {"name": "test_api", "version": "0.7.4", "dependencies": ["async", "boolean_selector", "collection", "meta", "source_span", "stack_trace", "stream_channel", "string_scanner", "term_glyph"]}, {"name": "sky_engine", "version": "0.0.0", "dependencies": []}, {"name": "permission_handler_android", "version": "9.0.2+1", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "permission_handler_windows", "version": "0.1.3", "dependencies": ["flutter", "permission_handler_platform_interface"]}, {"name": "flutter_spinkit", "version": "5.2.2", "dependencies": ["flutter"]}, {"name": "flutter_lints", "version": "1.0.4", "dependencies": ["lints"]}, {"name": "lints", "version": "1.0.1", "dependencies": []}, {"name": "cupertino_icons", "version": "1.0.8", "dependencies": []}, {"name": "permission_handler_platform_interface", "version": "3.12.0", "dependencies": ["flutter", "meta", "plugin_platform_interface"]}, {"name": "path_provider", "version": "2.1.5", "dependencies": ["flutter", "path_provider_android", "path_provider_foundation", "path_provider_linux", "path_provider_platform_interface", "path_provider_windows"]}, {"name": "path_provider_linux", "version": "2.2.1", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface", "xdg_directories"]}, {"name": "path_provider_windows", "version": "2.3.0", "dependencies": ["ffi", "flutter", "path", "path_provider_platform_interface"]}, {"name": "path_provider_platform_interface", "version": "2.1.2", "dependencies": ["flutter", "platform", "plugin_platform_interface"]}, {"name": "plugin_platform_interface", "version": "2.1.8", "dependencies": ["meta"]}, {"name": "path_provider_foundation", "version": "2.4.2", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "ffi", "version": "2.1.4", "dependencies": []}, {"name": "platform", "version": "3.1.6", "dependencies": []}, {"name": "xdg_directories", "version": "1.1.0", "dependencies": ["meta", "path"]}, {"name": "path_provider_android", "version": "2.2.17", "dependencies": ["flutter", "path_provider_platform_interface"]}, {"name": "permission_handler_apple", "version": "9.1.4", "dependencies": ["flutter", "permission_handler_platform_interface"]}], "configVersion": 1}